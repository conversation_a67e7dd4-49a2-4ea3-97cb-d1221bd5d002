import 'package:flutter/material.dart';
import 'package:listview/Manage_State/My_State.dart';
import 'package:provider/provider.dart';
class My_second_dialog extends StatelessWidget {
  Color color_ = Colors.white ;
  String text_ = '' ; int index_ ; 
  My_second_dialog({
    super.key,  required this.index_
  });
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: 5,
              blurRadius: 15,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'تعديل المهمة',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
            ),
            SizedBox(height: 30),
            Consumer<My_State>(
              builder: (context, state, child) => My_textfield(state: state),
            ),
            SizedBox(height: 30),
            Text(
              'الاهمية',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Consumer<My_State>(
                  builder: (context, state, child) => Circle(state: state, color: Colors.redAccent, tetx: 'عاجل'),
                ),
                Consumer<My_State>(
                  builder: (context, state, child) => Circle(state: state, color: const Color.fromARGB(255, 255, 192, 55), tetx: 'مهم'),
                ),
                Consumer<My_State>(
                  builder: (context, state, child) => Circle(state: state, color: Colors.green, tetx: 'عادي'),
                ),
              ],
            ),
            SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () { 
                    Provider.of<My_State>(context, listen: false).deleteTask(index_) ;
                    Navigator.pop(context);
                     } ,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade400,
                    padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: Text(
                    'حذف',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Provider.of<My_State>(context, listen: false).updateTask(index_);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: Text(
                    'موافقة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// This class is no longer needed as its functionality has been integrated into My_second_dialog
// Keeping this comment for reference

class My_textfield extends StatelessWidget {
  const My_textfield({
    super.key,
    required this.state,
  });
  final My_State state;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: TextField(
        onChanged: (value){ state.editedTaskTitle = value; state.isTitleEdited = true; },
        decoration: InputDecoration(
          hintText: 'ادخل العنوان الجديد هنا...',
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(20),
        ),
      ),
    );
  }
}

// This class is no longer needed as we're using ElevatedButton directly in My_second_dialog

class Circle extends StatelessWidget {
  const Circle({
    super.key,
    required this.state, required this.color, required this.tetx
  });

  final My_State state; final Color color; final String tetx;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: InkWell(
        onTap: () {
          state.editedTaskDescription = tetx;
          state.selectedColor = color;
          state.isDescriptionEdited = true;
        },
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                getIconForPriority(tetx),
                color: color,
                size: 30
              ),
            ),
            SizedBox(height: 8),
            Text(
              tetx,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  IconData getIconForPriority(String priority) {
    if (priority == 'عاجل') return Icons.priority_high;
    if (priority == 'مهم') return Icons.star;
    return Icons.check_circle;
  }
}
