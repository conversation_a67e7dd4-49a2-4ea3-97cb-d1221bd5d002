import 'package:flutter/material.dart';
import 'package:listview/Manage_State/My_State.dart';
import 'package:provider/provider.dart';
import 'Pages/My_second_dialog.dart';
class My_list extends StatefulWidget {
  int index_ ;
  Color color ; String text ; bool ch_ = false ; String text_ ;
   My_list({super.key , required this.color , required this.text , 
   required this.ch_ , required this.text_ , required this.index_
   });

  @override
  State<My_list> createState() => _My_listState();
}

class _My_listState extends State<My_list> {
    void dailog(BuildContext context) {
    showDialog(
      context: context, 
      builder: (context) => My_second_dialog(index_: widget.index_,),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = Provider.of<My_State>(context);
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            leading: Container(
              decoration: BoxDecoration(
                color: widget.color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              padding: EdgeInsets.all(8),
              child: Transform.scale(
                scale: 1.2,
                child: Checkbox(
                  value: widget.ch_,
                  onChanged: (val) {
                    setState(() {
                       widget.ch_ = val!; 
                       if(val) state.activeTaskCount-- ;
                       else state.activeTaskCount++ ;
                       state.updateTaskCount() ;
                    });
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  activeColor: widget.color,
                ),
              ),
            ),
            title: Text(
              widget.text,
              style: TextStyle(
                fontSize: 22, fontWeight: FontWeight.bold,
                color: Colors.black87,
                decoration: widget.ch_ ? TextDecoration.lineThrough : null,
                decorationColor: const Color.fromARGB(255, 69, 69, 69),
                decorationThickness: 4,
              ),
            ),
            subtitle: Text(
              widget.text_,
              style: TextStyle(
                fontSize: 18,
                color: widget.color,
                fontWeight: FontWeight.bold,
              ),
            ),
            trailing: IconButton(
              icon: Icon(Icons.edit, color: Colors.grey , size: 30,),
              onPressed: () => dailog(context),
            ),
          ),
        ),
      );
  }
}
