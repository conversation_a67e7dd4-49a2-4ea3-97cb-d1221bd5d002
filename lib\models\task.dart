import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

part 'task.g.dart';

@HiveType(typeId: 0)
class Task {
  @HiveField(0)
  String text;

  @HiveField(1)
  String text_;

  @HiveField(2)
  bool ch_;

  @HiveField(3)
  int colorValue;

  @HiveField(4)
  int index_;

  Task({
    required this.text,
    required this.text_,
    required this.ch_,
    required this.colorValue,
    required this.index_,
  });

  // Convert Color to int for storage
  factory Task.fromMyList(dynamic myList) {
    return Task(
      text: myList.text,
      text_: myList.text_,
      ch_: myList.ch_,
      colorValue: myList.color.value,
      index_: myList.index_,
    );
  }

  // Get Color from stored int value
  Color get color => Color(colorValue);
}
