import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:listview/list.dart';
import 'package:listview/models/task.dart';

class My_State with ChangeNotifier {
  bool isCompleted = false;
  String newTaskTitle = '', newTaskDescription = '', editedTaskTitle = ' ', editedTaskDescription = ' ';
  List<My_list> tasks = [];
  int activeTaskCount = 0;
  Color defaultColor = Colors.white, selectedColor = Colors.white;
  bool isTitleEdited = false, isDescriptionEdited = false;

  // Reference to the Hive box
  final Box<Task> _tasksBox = Hive.box<Task>('tasksBox');

  My_State() {
    // Load tasks from Hive when the state is initialized
    _loadTasksFromHive();
  }

  // Load tasks from Hive
  void _loadTasksFromHive() {
    if (_tasksBox.isNotEmpty) {
      final hiveTasks = _tasksBox.values.toList();

      // Sort tasks by index
      hiveTasks.sort((a, b) => a.index_.compareTo(b.index_));

      // Convert Hive tasks to My_list tasks
      tasks = hiveTasks.map((task) => My_list(
        color: task.color,
        text: task.text,
        ch_: task.ch_,
        text_: task.text_,
        index_: task.index_,
      )).toList();

      // Update the length counter
      activeTaskCount = tasks.where((task) => !task.ch_).length;

      notifyListeners();
    }
  }

  // Save tasks to Hive
  void _saveTasksToHive() {
    // Clear the box first
    _tasksBox.clear();

    // Add all tasks to the box
    for (var task in tasks) {
      _tasksBox.add(Task.fromMyList(task));
    }
  }

  void deleteTask(int index) {
    tasks.removeAt(index);
    if (activeTaskCount > 0) activeTaskCount--;

    // Update indices of remaining tasks
    for (int i = 0; i < tasks.length; i++) {
      tasks[i].index_ = i;
    }

    // Save changes to Hive
    _saveTasksToHive();

    notifyListeners();
  }

  void updateTask(int index) {
    if (isTitleEdited) tasks[index].text = editedTaskTitle;
    if (isDescriptionEdited) {
      tasks[index].color = selectedColor;
      tasks[index].text_ = editedTaskDescription;
    }
    if (isTitleEdited && isDescriptionEdited) {
      tasks[index].text = editedTaskTitle;
      tasks[index].color = selectedColor;
      tasks[index].text_ = editedTaskDescription;
    }

    // Save changes to Hive
    _saveTasksToHive();

    notifyListeners();
  }

  void addTask() {
    tasks.add(My_list(
      color: defaultColor,
      text: newTaskTitle,
      ch_: isCompleted,
      text_: newTaskDescription,
      index_: tasks.length
    ));
    activeTaskCount++;

    // Save changes to Hive
    _saveTasksToHive();

    notifyListeners();
  }

  void updateTaskCount() {
    // Update the length counter
    activeTaskCount = tasks.where((task) => !task.ch_).length;

    // Save changes to Hive
    _saveTasksToHive();

    notifyListeners();
  }
}
